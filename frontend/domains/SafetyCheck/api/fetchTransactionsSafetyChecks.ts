import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { parse as parseJ<PERSON><PERSON> } from '@zeal/toolkit/JSON'
import { string } from '@zeal/toolkit/Result'

import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

import { TransactionSafetyCheck } from '../SafetyCheck'

type RequestToCheck =
    | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
    | { type: 'user_operation'; initialUserOperation: InitialUserOperation }

const requestToTransactionParam = (
    request: RequestToCheck
): EthSendTransaction['params'][0] => {
    switch (request.type) {
        case 'rpc_request':
            return request.rpcRequest.params[0]
        case 'user_operation':
            const { callData, entrypoint, sender } =
                request.initialUserOperation
            return {
                data: callData,
                from: entrypoint,
                to: sender,
            }

        default:
            return notReachable(request)
    }
}

const BLOCKAID_NETWORK: Record<PredefinedNetwork['name'], string> = {
    Ethereum: 'ethereum',
    BSC: 'bsc',
    Polygon: 'polygon',
    Avalanche: 'avalanche',
    Optimism: 'optimism',
    Arbitrum: 'arbitrum',
    Base: 'base',
    Gnosis: 'gnosis',

    // FIXME @resetko-zeal those are supported seems like, check
    PolygonZkevm: '-',
    Blast: '-',
    Aurora: '-',
    Linea: '-',
    OPBNB: '-',
    Celo: '-',
    Fantom: '-',
    zkSync: '-',
    Cronos: '-',
    Mantle: '-',
    Manta: '-',
}

export const fetchTransactionsSafetyChecks = async ({
    requestToCheck,
    network,
    dApp,
    signal,
}: {
    requestToCheck: RequestToCheck
    network: PredefinedNetwork
    dApp: DAppSiteInfo | null
    signal?: AbortSignal
}): Promise<TransactionSafetyCheck[]> => {
    try {
        const trx = requestToTransactionParam(requestToCheck)

        const response = string(
            await post(
                `/proxy/ba/${BLOCKAID_NETWORK[network.name]}/v0/validate/transaction`,
                {
                    body: {
                        options: ['validation'],
                        metadata: dApp
                            ? { domain: dApp.hostname }
                            : { non_dapp: true },
                        data: {
                            from: trx.from,
                            data: trx.data,
                            value: trx.value || '0x00',
                            to: trx.to,
                        },
                    },
                },
                signal
            )
        )
            .andThen(parseJSON)
            .getSuccessResultOrThrow('Failed to blockaid safety checks')

        console.log('blockaid', response)

        return []
    } catch (error) {
        captureError(error)
        return []
    }
}
